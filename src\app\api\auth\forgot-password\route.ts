import { NextRequest, NextResponse } from 'next/server';
import { dbOperations, ForgotPasswordData } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const body: ForgotPasswordData = await request.json();
    
    // Validate input
    if (!body.username || !body.recovery_key || !body.new_password) {
      return NextResponse.json(
        { success: false, message: 'Username, recovery key, and new password are required' },
        { status: 400 }
      );
    }

    // Validate username format
    if (body.username.length < 3 || body.username.length > 50) {
      return NextResponse.json(
        { success: false, message: 'Username must be between 3 and 50 characters' },
        { status: 400 }
      );
    }

    // Validate new password strength
    if (body.new_password.length < 8) {
      return NextResponse.json(
        { success: false, message: 'New password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Validate recovery key
    if (body.recovery_key.length < 12) {
      return NextResponse.json(
        { success: false, message: 'Recovery key must be at least 12 characters long' },
        { status: 400 }
      );
    }

    const result = await dbOperations.resetPassword(body);
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Forgot password API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
