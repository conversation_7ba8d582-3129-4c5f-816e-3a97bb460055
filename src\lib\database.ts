import initSqlJs, { Database } from 'sql.js';
import bcrypt from 'bcryptjs';
import path from 'path';
import fs from 'fs';

// Database path
const dbPath = path.join(process.cwd(), 'data', 'ldis.db');

// Database instance
let db: Database | null = null;

// Initialize database function
async function initializeDB() {
  try {
    // Ensure data directory exists
    const dataDir = path.dirname(dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    const SQL = await initSqlJs();

    // Try to load existing database
    let database;
    if (fs.existsSync(dbPath)) {
      const filebuffer = fs.readFileSync(dbPath);
      database = new SQL.Database(filebuffer);
    } else {
      database = new SQL.Database();
    }

    console.log('Database initialized successfully');
    return database;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

// Get database instance
async function getDB() {
  if (!db) {
    db = await initializeDB();
  }
  return db;
}

// Save database to file
async function saveDB() {
  if (db) {
    const data = db.export();
    const buffer = Buffer.from(data);
    fs.writeFileSync(dbPath, buffer);
  }
}

// User interface
export interface User {
  id: number;
  username: string;
  password: string;
  recovery_key: string;
  created_at: string;
}

// Database row interface
interface UserRow {
  id: number;
  username: string;
  password: string;
  recovery_key: string;
  created_at: string;
}

interface CountRow {
  count: number;
}

// Initialize database schema
export async function initializeDatabase() {
  try {
    const database = await getDB();

    // Create users table
    database.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        recovery_key TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Save database to file
    await saveDB();

    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database schema:', error);
    throw error;
  }
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Generate recovery key
export function generateRecoveryKey(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
    if (i > 0 && i % 4 === 3 && i < 15) {
      result += '-';
    }
  }
  return result;
}

// Database operations
export const userOperations = {
  // Check if any user exists
  async hasUsers(): Promise<boolean> {
    try {
      const database = await getDB();
      const stmt = database.prepare('SELECT COUNT(*) as count FROM users');
      const result = stmt.getAsObject() as unknown as CountRow;
      stmt.free();
      return result.count > 0;
    } catch (error) {
      console.error('Failed to check users:', error);
      return false;
    }
  },

  // Create user (only if no users exist)
  async createUser(username: string, password: string, recoveryKey: string): Promise<User | null> {
    const hasUsers = await this.hasUsers();
    if (hasUsers) {
      throw new Error('A user already exists. Only one user is allowed.');
    }

    try {
      const database = await getDB();
      const hashedPassword = await hashPassword(password);

      // Insert user
      const insertStmt = database.prepare('INSERT INTO users (username, password, recovery_key) VALUES (?, ?, ?)');
      insertStmt.run([username, hashedPassword, recoveryKey]);
      insertStmt.free();

      // Get the created user
      const selectStmt = database.prepare('SELECT id, username, recovery_key, created_at FROM users WHERE username = ?');
      const result = selectStmt.getAsObject([username]) as unknown as UserRow;
      selectStmt.free();

      // Save database
      await saveDB();

      return result as User;
    } catch (error) {
      console.error('Failed to create user:', error);
      throw error;
    }
  },

  // Authenticate user
  async authenticateUser(username: string, password: string): Promise<User | null> {
    try {
      const database = await getDB();

      const stmt = database.prepare('SELECT * FROM users WHERE username = ?');
      const result = stmt.getAsObject([username]) as unknown as UserRow;
      stmt.free();

      if (!result || !result.id) {
        return null;
      }

      const isValid = await verifyPassword(password, result.password);
      if (!isValid) {
        return null;
      }

      // Return user without password
      const { password: _pwd, ...userWithoutPassword } = result;
      return userWithoutPassword as User;
    } catch (error) {
      console.error('Failed to authenticate user:', error);
      return null;
    }
  },

  // Reset password using recovery key
  async resetPassword(username: string, recoveryKey: string, newPassword: string): Promise<boolean> {
    try {
      const database = await getDB();

      // Find user with username and recovery key
      const selectStmt = database.prepare('SELECT * FROM users WHERE username = ? AND recovery_key = ?');
      const user = selectStmt.getAsObject([username, recoveryKey]) as unknown as UserRow;
      selectStmt.free();

      if (!user || !user.id) {
        return false;
      }

      // Update password
      const hashedPassword = await hashPassword(newPassword);
      const updateStmt = database.prepare('UPDATE users SET password = ? WHERE id = ?');
      updateStmt.run([hashedPassword, user.id]);
      updateStmt.free();

      // Save database
      await saveDB();

      return true;
    } catch (error) {
      console.error('Failed to reset password:', error);
      return false;
    }
  },

  // Get user by username
  async getUserByUsername(username: string): Promise<User | null> {
    try {
      const database = await getDB();

      const stmt = database.prepare('SELECT id, username, recovery_key, created_at FROM users WHERE username = ?');
      const result = stmt.getAsObject([username]) as unknown as UserRow;
      stmt.free();

      return result && result.id ? (result as User) : null;
    } catch (error) {
      console.error('Failed to get user:', error);
      return null;
    }
  },

  // Get user by ID
  async getUserById(id: number): Promise<User | null> {
    try {
      const database = await getDB();

      const stmt = database.prepare('SELECT id, username, recovery_key, created_at FROM users WHERE id = ?');
      const result = stmt.getAsObject([id]) as unknown as UserRow;
      stmt.free();

      return result && result.id ? (result as User) : null;
    } catch (error) {
      console.error('Failed to get user by ID:', error);
      return null;
    }
  }
};

// Initialize database on first access
export async function ensureInitialized() {
  await initializeDatabase();
}

export default getDB;
