import Database from 'better-sqlite3';
import bcrypt from 'bcryptjs';
import path from 'path';
import fs from 'fs';

const DB_PATH = path.join(process.cwd(), 'data', 'ldis.db');

// Ensure data directory exists
const dataDir = path.dirname(DB_PATH);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Initialize database
const db = new Database(DB_PATH);

// Enable WAL mode for better performance
db.pragma('journal_mode = WAL');

// Create users table
const createUsersTable = `
  CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    recovery_key TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )
`;

db.exec(createUsersTable);

export interface User {
  id: number;
  username: string;
  password: string;
  recovery_key: string;
  created_at: string;
}

export interface CreateUserData {
  username: string;
  password: string;
  recovery_key: string;
}

export interface SignInData {
  username: string;
  password: string;
}

export interface ForgotPasswordData {
  username: string;
  recovery_key: string;
  new_password: string;
}

// Database operations
export const dbOperations = {
  // Check if any users exist
  hasUsers: (): boolean => {
    const stmt = db.prepare('SELECT COUNT(*) as count FROM users');
    const result = stmt.get() as { count: number };
    return result.count > 0;
  },

  // Create a new user (only if no users exist)
  createUser: async (userData: CreateUserData): Promise<{ success: boolean; message: string }> => {
    try {
      // Check if users already exist
      if (dbOperations.hasUsers()) {
        return { success: false, message: 'A user already exists. Only one user is allowed.' };
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 12);
      
      // Hash recovery key
      const hashedRecoveryKey = await bcrypt.hash(userData.recovery_key, 12);

      const stmt = db.prepare(`
        INSERT INTO users (username, password, recovery_key)
        VALUES (?, ?, ?)
      `);

      stmt.run(userData.username, hashedPassword, hashedRecoveryKey);
      
      return { success: true, message: 'User created successfully' };
    } catch (error) {
      console.error('Error creating user:', error);
      return { success: false, message: 'Failed to create user' };
    }
  },

  // Sign in user
  signIn: async (signInData: SignInData): Promise<{ success: boolean; message: string; user?: Omit<User, 'password' | 'recovery_key'> }> => {
    try {
      const stmt = db.prepare('SELECT * FROM users WHERE username = ?');
      const user = stmt.get(signInData.username) as User | undefined;

      if (!user) {
        return { success: false, message: 'Invalid username or password' };
      }

      const isValidPassword = await bcrypt.compare(signInData.password, user.password);
      
      if (!isValidPassword) {
        return { success: false, message: 'Invalid username or password' };
      }

      // Return user without sensitive data
      const { password, recovery_key, ...safeUser } = user;
      return { 
        success: true, 
        message: 'Sign in successful',
        user: safeUser
      };
    } catch (error) {
      console.error('Error signing in:', error);
      return { success: false, message: 'Sign in failed' };
    }
  },

  // Reset password using recovery key
  resetPassword: async (resetData: ForgotPasswordData): Promise<{ success: boolean; message: string }> => {
    try {
      const stmt = db.prepare('SELECT * FROM users WHERE username = ?');
      const user = stmt.get(resetData.username) as User | undefined;

      if (!user) {
        return { success: false, message: 'User not found' };
      }

      const isValidRecoveryKey = await bcrypt.compare(resetData.recovery_key, user.recovery_key);
      
      if (!isValidRecoveryKey) {
        return { success: false, message: 'Invalid recovery key' };
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(resetData.new_password, 12);

      const updateStmt = db.prepare('UPDATE users SET password = ? WHERE username = ?');
      updateStmt.run(hashedPassword, resetData.username);

      return { success: true, message: 'Password reset successfully' };
    } catch (error) {
      console.error('Error resetting password:', error);
      return { success: false, message: 'Password reset failed' };
    }
  },

  // Get user by username (for internal use)
  getUserByUsername: (username: string): Omit<User, 'password' | 'recovery_key'> | null => {
    try {
      const stmt = db.prepare('SELECT id, username, created_at FROM users WHERE username = ?');
      const user = stmt.get(username) as Omit<User, 'password' | 'recovery_key'> | undefined;
      return user || null;
    } catch (error) {
      console.error('Error getting user:', error);
      return null;
    }
  }
};

export default db;
