import { NextResponse } from 'next/server';
import { dbOperations } from '@/lib/database';

export async function GET() {
  try {
    const hasUsers = dbOperations.hasUsers();
    
    return NextResponse.json(
      { hasUsers },
      { status: 200 }
    );
  } catch (error) {
    console.error('Check users API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
