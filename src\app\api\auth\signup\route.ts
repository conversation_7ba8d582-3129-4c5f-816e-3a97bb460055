import { NextRequest, NextResponse } from 'next/server';
import { dbOperations, CreateUserData } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const body: CreateUserData = await request.json();
    
    // Validate input
    if (!body.username || !body.password || !body.recovery_key) {
      return NextResponse.json(
        { success: false, message: 'Username, password, and recovery key are required' },
        { status: 400 }
      );
    }

    // Validate username format
    if (body.username.length < 3 || body.username.length > 50) {
      return NextResponse.json(
        { success: false, message: 'Username must be between 3 and 50 characters' },
        { status: 400 }
      );
    }

    // Check for valid username characters (alphanumeric and underscore only)
    if (!/^[a-zA-Z0-9_]+$/.test(body.username)) {
      return NextResponse.json(
        { success: false, message: 'Username can only contain letters, numbers, and underscores' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (body.password.length < 8) {
      return NextResponse.json(
        { success: false, message: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Validate recovery key
    if (body.recovery_key.length < 12) {
      return NextResponse.json(
        { success: false, message: 'Recovery key must be at least 12 characters long' },
        { status: 400 }
      );
    }

    const result = await dbOperations.createUser(body);
    
    if (result.success) {
      return NextResponse.json(result, { status: 201 });
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Sign up API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
