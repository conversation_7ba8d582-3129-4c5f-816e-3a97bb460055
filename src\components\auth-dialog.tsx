"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

interface AuthDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAuthSuccess: (user: { id: number; username: string; created_at: string }) => void;
}

type AuthMode = "signin" | "signup" | "forgot";

export function AuthDialog({ open, onOpenChange, onAuthSuccess }: AuthDialogProps) {
  const [mode, setMode] = useState<AuthMode>("signin");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [hasUsers, setHasUsers] = useState(true);
  
  // Form states
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [recoveryKey, setRecoveryKey] = useState("");
  const [newPassword, setNewPassword] = useState("");

  // Check if users exist when dialog opens
  useEffect(() => {
    if (open) {
      checkUsers();
      // Reset form when dialog opens
      resetForm();
    }
  }, [open]);

  const checkUsers = async () => {
    try {
      const response = await fetch("/api/auth/check-users");
      const data = await response.json();
      setHasUsers(data.hasUsers);
      
      // If no users exist, default to signup mode
      if (!data.hasUsers) {
        setMode("signup");
      }
    } catch (error) {
      console.error("Error checking users:", error);
    }
  };

  const resetForm = () => {
    setUsername("");
    setPassword("");
    setRecoveryKey("");
    setNewPassword("");
    setError("");
    setLoading(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      let endpoint = "";
      let body = {};

      switch (mode) {
        case "signin":
          endpoint = "/api/auth/signin";
          body = { username, password };
          break;
        case "signup":
          endpoint = "/api/auth/signup";
          body = { username, password, recovery_key: recoveryKey };
          break;
        case "forgot":
          endpoint = "/api/auth/forgot-password";
          body = { username, recovery_key: recoveryKey, new_password: newPassword };
          break;
      }

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      if (data.success) {
        if (mode === "signin") {
          onAuthSuccess(data.user);
        } else if (mode === "signup") {
          // After successful signup, automatically sign in
          const signInResponse = await fetch("/api/auth/signin", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ username, password }),
          });
          
          const signInData = await signInResponse.json();
          if (signInData.success) {
            onAuthSuccess(signInData.user);
          }
        } else if (mode === "forgot") {
          // After password reset, switch to signin mode
          setMode("signin");
          setError("");
          setPassword("");
          setRecoveryKey("");
          setNewPassword("");
        }
        
        if (mode !== "forgot") {
          onOpenChange(false);
          resetForm();
        }
      } else {
        setError(data.message);
      }
    } catch (error) {
      console.error("Auth error:", error);
      setError("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  const getTitle = () => {
    switch (mode) {
      case "signin":
        return "Sign In";
      case "signup":
        return "Sign Up";
      case "forgot":
        return "Reset Password";
    }
  };

  const getDescription = () => {
    switch (mode) {
      case "signin":
        return "Enter your credentials to access admin mode";
      case "signup":
        return "Create an admin account to access admin features";
      case "forgot":
        return "Use your recovery key to reset your password";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <DialogDescription>{getDescription()}</DialogDescription>
        </DialogHeader>

        {/* Tab-like navigation */}
        <div className="flex space-x-1 rounded-lg bg-muted p-1">
          <button
            type="button"
            onClick={() => setMode("signin")}
            disabled={!hasUsers}
            className={cn(
              "flex-1 rounded-md px-3 py-1.5 text-sm font-medium transition-all",
              mode === "signin"
                ? "bg-background text-foreground shadow-sm"
                : "text-muted-foreground hover:text-foreground",
              !hasUsers && "opacity-50 cursor-not-allowed"
            )}
          >
            Sign In
          </button>
          <button
            type="button"
            onClick={() => setMode("signup")}
            disabled={hasUsers}
            className={cn(
              "flex-1 rounded-md px-3 py-1.5 text-sm font-medium transition-all",
              mode === "signup"
                ? "bg-background text-foreground shadow-sm"
                : "text-muted-foreground hover:text-foreground",
              hasUsers && "opacity-50 cursor-not-allowed"
            )}
          >
            Sign Up
          </button>
          <button
            type="button"
            onClick={() => setMode("forgot")}
            disabled={!hasUsers}
            className={cn(
              "flex-1 rounded-md px-3 py-1.5 text-sm font-medium transition-all",
              mode === "forgot"
                ? "bg-background text-foreground shadow-sm"
                : "text-muted-foreground hover:text-foreground",
              !hasUsers && "opacity-50 cursor-not-allowed"
            )}
          >
            Forgot
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
              {error}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              disabled={loading}
            />
          </div>

          {(mode === "signin" || mode === "signup") && (
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={loading}
              />
            </div>
          )}

          {(mode === "signup" || mode === "forgot") && (
            <div className="space-y-2">
              <Label htmlFor="recoveryKey">Recovery Key</Label>
              <Input
                id="recoveryKey"
                type="password"
                value={recoveryKey}
                onChange={(e) => setRecoveryKey(e.target.value)}
                required
                disabled={loading}
                placeholder="Keep this safe - you'll need it to reset your password"
              />
            </div>
          )}

          {mode === "forgot" && (
            <div className="space-y-2">
              <Label htmlFor="newPassword">New Password</Label>
              <Input
                id="newPassword"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                required
                disabled={loading}
              />
            </div>
          )}

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? "Processing..." : getTitle()}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}
