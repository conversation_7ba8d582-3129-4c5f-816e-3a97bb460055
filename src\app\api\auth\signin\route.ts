import { NextRequest, NextResponse } from 'next/server';
import { dbOperations, SignInData } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const body: SignInData = await request.json();
    
    // Validate input
    if (!body.username || !body.password) {
      return NextResponse.json(
        { success: false, message: 'Username and password are required' },
        { status: 400 }
      );
    }

    // Validate username format (basic validation)
    if (body.username.length < 3 || body.username.length > 50) {
      return NextResponse.json(
        { success: false, message: 'Username must be between 3 and 50 characters' },
        { status: 400 }
      );
    }

    // Validate password format
    if (body.password.length < 6) {
      return NextResponse.json(
        { success: false, message: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    const result = await dbOperations.signIn(body);
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: 401 });
    }
  } catch (error) {
    console.error('Sign in API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
