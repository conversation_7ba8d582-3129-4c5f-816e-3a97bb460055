"use client";

import { useState, useEffect } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { AuthDialog } from "@/components/auth-dialog";
import { Settings, Shield, User, LogOut } from "lucide-react";

interface User {
  id: number;
  username: string;
  created_at: string;
}

export default function SettingsPage() {
  const [adminMode, setAdminMode] = useState(false);
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  // Load admin mode state from localStorage on component mount
  useEffect(() => {
    const savedAdminMode = localStorage.getItem("adminMode");
    const savedUser = localStorage.getItem("currentUser");
    
    if (savedAdminMode === "true" && savedUser) {
      setAdminMode(true);
      setCurrentUser(JSON.parse(savedUser));
    }
  }, []);

  const handleAdminModeToggle = (checked: boolean) => {
    if (checked) {
      // User wants to enable admin mode - show auth dialog
      setShowAuthDialog(true);
    } else {
      // User wants to disable admin mode
      setAdminMode(false);
      setCurrentUser(null);
      localStorage.removeItem("adminMode");
      localStorage.removeItem("currentUser");
    }
  };

  const handleAuthSuccess = (user: User) => {
    setAdminMode(true);
    setCurrentUser(user);
    localStorage.setItem("adminMode", "true");
    localStorage.setItem("currentUser", JSON.stringify(user));
  };

  const handleSignOut = () => {
    setAdminMode(false);
    setCurrentUser(null);
    localStorage.removeItem("adminMode");
    localStorage.removeItem("currentUser");
  };

  return (
    <div className="container mx-auto max-w-4xl p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <Settings className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold">Settings</h1>
            <p className="text-muted-foreground">
              Manage your application preferences and admin access
            </p>
          </div>
        </div>

        {/* Admin Mode Section */}
        <div className="rounded-lg border bg-card p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <Shield className="h-5 w-5 text-primary" />
              <h2 className="text-xl font-semibold">Admin Mode</h2>
            </div>
            
            <p className="text-sm text-muted-foreground">
              Enable admin mode to access advanced features and administrative functions.
              You'll need to sign in with your admin credentials.
            </p>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="admin-mode"
                  checked={adminMode}
                  onCheckedChange={handleAdminModeToggle}
                />
                <Label htmlFor="admin-mode" className="text-sm font-medium">
                  {adminMode ? "Admin Mode Enabled" : "Enable Admin Mode"}
                </Label>
              </div>

              {adminMode && currentUser && (
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <User className="h-4 w-4" />
                    <span>Signed in as <strong>{currentUser.username}</strong></span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSignOut}
                    className="flex items-center gap-2"
                  >
                    <LogOut className="h-4 w-4" />
                    Sign Out
                  </Button>
                </div>
              )}
            </div>

            {adminMode && (
              <div className="mt-4 p-4 bg-primary/10 rounded-md border border-primary/20">
                <div className="flex items-center gap-2 text-sm text-primary">
                  <Shield className="h-4 w-4" />
                  <span className="font-medium">Admin mode is active</span>
                </div>
                <p className="text-xs text-primary/80 mt-1">
                  You now have access to administrative features throughout the application.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Additional Settings Sections */}
        <div className="rounded-lg border bg-card p-6">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">General Settings</h2>
            <p className="text-sm text-muted-foreground">
              Additional application settings will be available here.
            </p>
            
            {/* Placeholder for future settings */}
            <div className="space-y-3">
              <div className="flex items-center justify-between py-2">
                <div>
                  <Label className="text-sm font-medium">Theme</Label>
                  <p className="text-xs text-muted-foreground">
                    Choose your preferred theme (managed by theme toggle in header)
                  </p>
                </div>
              </div>
              
              <div className="flex items-center justify-between py-2">
                <div>
                  <Label className="text-sm font-medium">Notifications</Label>
                  <p className="text-xs text-muted-foreground">
                    Configure notification preferences
                  </p>
                </div>
                <Switch disabled />
              </div>
            </div>
          </div>
        </div>

        {/* System Information */}
        {adminMode && (
          <div className="rounded-lg border bg-card p-6">
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">System Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="font-medium">Application</Label>
                  <p className="text-muted-foreground">Legal Document Issuance System</p>
                </div>
                <div>
                  <Label className="font-medium">Version</Label>
                  <p className="text-muted-foreground">1.0.0</p>
                </div>
                <div>
                  <Label className="font-medium">Admin User</Label>
                  <p className="text-muted-foreground">{currentUser?.username}</p>
                </div>
                <div>
                  <Label className="font-medium">Account Created</Label>
                  <p className="text-muted-foreground">
                    {currentUser?.created_at ? new Date(currentUser.created_at).toLocaleDateString() : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Authentication Dialog */}
      <AuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        onAuthSuccess={handleAuthSuccess}
      />
    </div>
  );
}
